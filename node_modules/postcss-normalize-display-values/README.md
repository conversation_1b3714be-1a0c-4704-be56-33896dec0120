# [postcss][postcss]-normalize-display-values

> Normalize display property values with PostCSS.

## Install

With [npm](https://npmjs.org/package/postcss-normalize-display-values) do:

```
npm install postcss-normalize-display-values --save
```

## Example

### Input

```css
div {
    display: inline flow-root
}
```

### Output

```css
div {
    display: inline-block
}
``` 

## Usage

See the [PostCSS documentation](https://github.com/postcss/postcss#usage) for
examples for your environment.

## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).

## License

MIT © [<PERSON>](http://beneb.info)

[postcss]: https://github.com/postcss/postcss
