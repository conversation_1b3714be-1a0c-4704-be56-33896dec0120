{"name": "postcss-normalize-whitespace", "version": "5.1.1", "description": "Trim whitespace inside and around CSS rules & declarations.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["src", "LICENSE-MIT", "types"], "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"postcss-value-parser": "^4.2.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}, "readme": "# [postcss][postcss]-normalize-whitespace\n\n> Normalize whitespace with PostCSS.\n\n## Install\n\nWith [npm](https://npmjs.org/package/postcss-normalize-whitespace) do:\n\n```\nnpm install postcss-normalize-whitespace --save\n```\n\n## Example\n\n### Input\n\n```css\nh1{\n    width: calc(10px - ( 100px / var(--test) )) \n}\n```\n\n### Output\n\n```css\nh1{\n    width: calc(10px - 100px / var(--test))\n}\n``` \n\n## Usage\n\nSee the [PostCSS documentation](https://github.com/postcss/postcss#usage) for\nexamples for your environment.\n\n## Contributors\n\nSee [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).\n\n## License\n\nMIT © [<PERSON>](http://beneb.info)\n\n[postcss]: https://github.com/postcss/postcss\n"}