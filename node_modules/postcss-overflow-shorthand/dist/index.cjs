"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=e(require("postcss-value-parser"));const r=e=>{const r=!("preserve"in Object(e))||Boolean(e.preserve);return{postcssPlugin:"postcss-overflow-shorthand",Declaration:(e,{result:t})=>{if("overflow"!==e.prop.toLowerCase())return;let s="",a="";const l=e.value;try{const e=o.default(l).nodes.slice().filter((e=>"comment"!==e.type&&"space"!==e.type));if(e.length<2)return;s=o.default.stringify(e[0]),a=o.default.stringify(e[1])}catch(o){return void e.warn(t,`Failed to parse value '${l}' as a shorthand for "overflow". Leaving the original value intact.`)}s&&a&&(s.toLowerCase()===a.toLowerCase()?e.cloneBefore({value:s}):(e.cloneBefore({prop:"overflow-x",value:s}),e.cloneBefore({prop:"overflow-y",value:a})),r||e.remove())}}};r.postcss=!0,module.exports=r;
