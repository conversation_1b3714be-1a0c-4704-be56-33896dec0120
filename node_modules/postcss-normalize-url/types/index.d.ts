export = pluginCreator;
/** @typedef {normalize.Options} Options */
/**
 * @type {import('postcss').PluginCreator<Options>}
 * @param {Options} opts
 * @return {import('postcss').Plugin}
 */
declare function pluginCreator(opts: Options): import('postcss').Plugin;
declare namespace pluginCreator {
    export { postcss, Options };
}
type Options = normalize.Options;
declare var postcss: true;
import normalize = require("normalize-url");
