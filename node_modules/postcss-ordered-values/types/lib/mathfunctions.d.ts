declare const _exports: {
    add(value: string): Set<string>;
    clear(): void;
    delete(value: string): boolean;
    forEach(callbackfn: (value: string, value2: string, set: Set<string>) => void, thisArg?: any): void;
    has(value: string): boolean;
    readonly size: number;
    entries(): IterableIterator<[string, string]>;
    keys(): IterableIterator<string>;
    values(): IterableIterator<string>;
    [Symbol.iterator](): IterableIterator<string>;
    readonly [Symbol.toStringTag]: string;
};
export = _exports;
