"use strict";var s=require("autoprefixer"),e=require("cssdb"),t=require("@csstools/postcss-progressive-custom-properties"),o=require("fs"),i=require("path"),r=require("browserslist"),a=require("postcss-initial"),n=require("postcss-pseudo-class-any-link"),c=require("css-blank-pseudo"),p=require("postcss-page-break"),u=require("@csstools/postcss-cascade-layers"),l=require("postcss-attribute-case-insensitive"),m=require("postcss-clamp"),d=require("@csstools/postcss-color-function"),f=require("postcss-color-functional-notation"),g=require("postcss-custom-media"),b=require("postcss-custom-properties"),h=require("postcss-custom-selectors"),N=require("postcss-dir-pseudo-class"),y=require("@csstools/postcss-normalize-display-values"),k=require("postcss-double-position-gradients"),v=require("postcss-env-function"),w=require("postcss-focus-visible"),q=require("postcss-focus-within"),$=require("@csstools/postcss-font-format-keywords"),O=require("postcss-font-variant"),S=require("postcss-gap-properties"),x=require("css-has-pseudo"),j=require("postcss-color-hex-alpha"),P=require("@csstools/postcss-hwb-function"),F=require("@csstools/postcss-ic-unit"),E=require("postcss-image-set-function"),C=require("@csstools/postcss-is-pseudo-class"),M=require("postcss-lab-function"),_=require("postcss-logical"),A=require("postcss-media-minmax"),R=require("@csstools/postcss-nested-calc"),U=require("postcss-nesting"),T=require("postcss-selector-not"),B=require("@csstools/postcss-oklab-function"),I=require("postcss-opacity-percentage"),V=require("postcss-overflow-shorthand"),W=require("postcss-replace-overflow-wrap"),L=require("postcss-place"),D=require("css-prefers-color-scheme"),H=require("postcss-color-rebeccapurple"),J=require("@csstools/postcss-stepped-value-functions"),z=require("@csstools/postcss-text-decoration-shorthand"),G=require("@csstools/postcss-trigonometric-functions"),K=require("@csstools/postcss-unset-value");function Q(s){return s&&"object"==typeof s&&"default"in s?s:{default:s}}var X=Q(s),Y=Q(e),Z=Q(t),ss=Q(o),es=Q(i),ts=Q(r),os=Q(a),is=Q(n),rs=Q(c),as=Q(p),ns=Q(u),cs=Q(l),ps=Q(m),us=Q(d),ls=Q(f),ms=Q(g),ds=Q(b),fs=Q(h),gs=Q(N),bs=Q(y),hs=Q(k),Ns=Q(v),ys=Q(w),ks=Q(q),vs=Q($),ws=Q(O),qs=Q(S),$s=Q(x),Os=Q(j),Ss=Q(P),xs=Q(F),js=Q(E),Ps=Q(C),Fs=Q(M),Es=Q(_),Cs=Q(A),Ms=Q(R),_s=Q(U),As=Q(T),Rs=Q(B),Us=Q(I),Ts=Q(V),Bs=Q(W),Is=Q(L),Vs=Q(D),Ws=Q(H),Ls=Q(J),Ds=Q(z),Hs=Q(G),Js=Q(K);const zs={"blank-pseudo-class":"https://github.com/csstools/postcss-plugins/blob/main/plugins/css-blank-pseudo/README-BROWSER.md","focus-visible-pseudo-class":"https://github.com/WICG/focus-visible","focus-within-pseudo-class":"https://github.com/jsxtools/focus-within/blob/master/README-BROWSER.md","has-pseudo-class":"https://github.com/csstools/postcss-plugins/blob/main/plugins/css-has-pseudo/README-BROWSER.md","prefers-color-scheme-query":"https://github.com/csstools/postcss-plugins/blob/main/plugins/css-prefers-color-scheme/README-BROWSER.md"},Gs=["blank-pseudo-class","focus-visible-pseudo-class","focus-within-pseudo-class","has-pseudo-class","prefers-color-scheme-query"];async function Ks(s,e,t,o){const i=function(s){return`:root {\n${Object.keys(s).reduce(((e,t)=>(e.push(`\t${t}: ${s[t]};`),e)),[]).join("\n")}\n}\n`}(t),r=function(s){return`${Object.keys(s).reduce(((e,t)=>(e.push(`@custom-media ${t} ${s[t]};`),e)),[]).join("\n")}\n`}(e),a=function(s){return`${Object.keys(s).reduce(((e,t)=>(e.push(`@custom-selector ${t} ${s[t]};`),e)),[]).join("\n")}\n`}(o),n=`${r}\n${a}\n${i}`;await se(s,n)}function Qs(s,e){return`\n\t${s}: {\n${Object.keys(e).reduce(((s,t)=>(s.push(`\t\t'${ee(t)}': '${ee(e[t])}'`),s)),[]).join(",\n")}\n\t}`}function Xs(s,e){return`export const ${s} = {\n${Object.keys(e).reduce(((s,t)=>(s.push(`\t'${ee(t)}': '${ee(e[t])}'`),s)),[]).join(",\n")}\n};\n`}function Ys(s,e){return Promise.all([].concat(e).map((async e=>{if(e instanceof Function)await e({customMedia:Zs(s.customMedia),customProperties:Zs(s.customProperties),customSelectors:Zs(s.customSelectors)});else{const t=e===Object(e)?e:{to:String(e)},o=t.toJSON||Zs;if("customMedia"in t||"customProperties"in t||"customSelectors"in t)t.customMedia=o(s.customMedia),t.customProperties=o(s.customProperties),t.customSelectors=o(s.customSelectors);else if("custom-media"in t||"custom-properties"in t||"custom-selectors"in t)t["custom-media"]=o(s.customMedia),t["custom-properties"]=o(s.customProperties),t["custom-selectors"]=o(s.customSelectors);else{const e=String(t.to||""),i=(t.type||es.default.extname(t.to).slice(1)).toLowerCase(),r=o(s.customMedia),a=o(s.customProperties),n=o(s.customSelectors);"css"===i&&await Ks(e,r,a,n),"js"===i&&await async function(s,e,t,o){const i=`module.exports = {${Qs("customMedia",e)},${Qs("customProperties",t)},${Qs("customSelectors",o)}\n};\n`;await se(s,i)}(e,r,a,n),"json"===i&&await async function(s,e,t,o){const i=`${JSON.stringify({"custom-media":e,"custom-properties":t,"custom-selectors":o},null,"  ")}\n`;await se(s,i)}(e,r,a,n),"mjs"===i&&await async function(s,e,t,o){const i=`${Xs("customMedia",e)}\n${Xs("customProperties",t)}\n${Xs("customSelectors",o)}`;await se(s,i)}(e,r,a,n)}}})))}function Zs(s){return Object.keys(s).reduce(((e,t)=>(e[t]=String(s[t]),e)),{})}function se(s,e){return new Promise(((t,o)=>{ss.default.writeFile(s,e,(s=>{s?o(s):t()}))}))}function ee(s){return s.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r")}function te(s,e){if(!s)return!1;if("string"==typeof s)return!0;if(Array.isArray(s)){for(let t=0;t<s.length;t++){if("string"==typeof s[t])return!0;if(s[t]&&e in Object(s[t]))return!0}return!1}return e in Object(s)}function oe(s,e,t){return Math.max(s,Math.min(e,t))}const ie=Symbol("insertBefore"),re=Symbol("insertAfter"),ae=Symbol("insertOrder"),ne=Symbol("plugin");function ce(s,e,t){if("insertBefore"!==t&&"insertAfter"!==t)return[];const o="insertBefore"===t?ie:re,i=[];for(const t in e){if(!Object.hasOwnProperty.call(e,t))continue;if(!s.find((s=>s.id===t)))continue;let r=e[t];Array.isArray(r)||(r=[r]);for(let s=0;s<r.length;s++)i.push({id:t,[ne]:r[s],[ae]:s,[o]:!0})}return i}var pe=["custom-media-queries","custom-properties","environment-variables","image-set-function","media-query-ranges","prefers-color-scheme-query","nesting-rules","custom-selectors","any-link-pseudo-class","case-insensitive-attributes","focus-visible-pseudo-class","focus-within-pseudo-class","not-pseudo-class","logical-properties-and-values","dir-pseudo-class","all-property","color-functional-notation","double-position-gradients","hexadecimal-alpha-notation","hwb-function","lab-function","rebeccapurple-color","blank-pseudo-class","break-properties","font-variant-property","is-pseudo-class","has-pseudo-class","gap-properties","overflow-property","overflow-wrap-property","place-properties","system-ui-font-family","cascade-layers","stepped-value-functions","trigonometric-functions"];function ue(){return{postcssPlugin:"postcss-system-ui-font",Declaration(s){le.test(s.prop)&&(s.value.includes(de.join(", "))||(s.value=s.value.replace(fe,ge)))}}}ue.postcss=!0;const le=/(?:^(?:-|\\002d){2})|(?:^font(?:-family)?$)/i,me="[\\f\\n\\r\\x09\\x20]",de=["system-ui","-apple-system","Segoe UI","Roboto","Ubuntu","Cantarell","Noto Sans","sans-serif"],fe=new RegExp(`(^|,|${me}+)(?:system-ui${me}*)(?:,${me}*(?:${de.join("|")})${me}*)?(,|$)`,"i"),ge=`$1${de.join(", ")}$2`,be=new Map([["all-property",os.default],["any-link-pseudo-class",is.default],["blank-pseudo-class",rs.default],["break-properties",as.default],["cascade-layers",ns.default],["case-insensitive-attributes",cs.default],["clamp",ps.default],["color-function",us.default],["color-functional-notation",ls.default],["custom-media-queries",ms.default],["custom-properties",ds.default],["custom-selectors",fs.default],["dir-pseudo-class",gs.default],["display-two-values",bs.default],["double-position-gradients",hs.default],["environment-variables",Ns.default],["focus-visible-pseudo-class",ys.default],["focus-within-pseudo-class",ks.default],["font-format-keywords",vs.default],["font-variant-property",ws.default],["gap-properties",qs.default],["has-pseudo-class",$s.default],["hexadecimal-alpha-notation",Os.default],["hwb-function",Ss.default],["ic-unit",xs.default],["image-set-function",js.default],["is-pseudo-class",Ps.default],["lab-function",Fs.default],["logical-properties-and-values",Es.default],["media-query-ranges",Cs.default],["nested-calc",Ms.default],["nesting-rules",_s.default],["not-pseudo-class",As.default],["oklab-function",Rs.default],["opacity-percentage",Us.default],["overflow-property",Ts.default],["overflow-wrap-property",Bs.default],["place-properties",Is.default],["prefers-color-scheme-query",Vs.default],["rebeccapurple-color",Ws.default],["stepped-value-functions",Ls.default],["system-ui-font-family",ue],["text-decoration-shorthand",Ds.default],["trigonometric-functions",Hs.default],["unset-value",Js.default]]);function he(s,e,t){return s.concat(ce(s,e,"insertBefore"),ce(s,t,"insertAfter")).filter((s=>function(s){return!!s[ie]||!!s[re]||!!be.has(s.id)}(s))).sort(((s,e)=>function(s,e){return s.id===e.id?s[ie]&&e[ie]||s[re]&&e[re]?oe(-1,s[ae]-e[ae],1):s[ie]||e[re]?-1:s[re]||e[ie]?1:0:oe(-1,pe.indexOf(s.id)-pe.indexOf(e.id),1)}(s,e)))}const Ne=["and_chr","and_ff","and_qq","and_uc","android","baidu","chrome","edge","firefox","ie","ie_mob","ios_saf","kaios","op_mini","op_mob","opera","safari","samsung"];function ye(s){if(!s)return[];if(!("browser_support"in s))return["> 0%"];const e=[];return Ne.forEach((t=>{if("op_mini"===t&&void 0===s.browser_support[t])return void e.push("op_mini all");const o=s.browser_support[t];"string"==typeof o&&/^[0-9|.]+$/.test(o)?e.push(`${t} < ${s.browser_support[t]}`):e.push(`${t} >= 1`)})),e}function ke(s,e,t,o){const i=ts.default(s,{ignoreUnknownVersions:!0});switch(e.id){case"is-pseudo-class":return{onComplexSelector:"warning"};case"nesting-rules":if(function(s,e){const t=ye(s);if(e.some((s=>ts.default(t,{ignoreUnknownVersions:!0}).some((e=>e===s)))))return!0;return!1}(t.find((s=>"is-pseudo-class"===s.id)),i))return o.log('Disabling :is on "nesting-rules" due to lack of browser support.'),{noIsPseudoSelector:!0};return{};case"any-link-pseudo-class":if(i.find((s=>s.startsWith("ie ")||s.startsWith("edge "))))return o.log('Adding area[href] fallbacks for ":any-link" support in Edge and IE.'),{subFeatures:{areaHrefNeedsFixing:!0}};return{};default:return{}}}function ve(s,e,t,o){const i=Object(e.features),r=!("enableClientSidePolyfills"in e)||e.enableClientSidePolyfills,a=Object(e.insertBefore),n=Object(e.insertAfter),c=e.browsers,p=oe(0,function(s){const e=parseInt(s,10);return Number.isNaN(e)?0:e}(e.minimumVendorImplementations),3);p>0&&o.log(`Using features with ${p} or more vendor implementations`);const u=function(s,e){let t=2;if(void 0===s.stage)return e.log(`Using features from Stage ${t} (default)`),t;if(!1===s.stage)t=5;else{let e=parseInt(s.stage,10);Number.isNaN(e)&&(e=0),t=oe(0,e,5)}return 5===t?e.log('Stage has been disabled, features will be handled via the "features" option.'):e.log(`Using features from Stage ${t}`),t}(e,o);2===u&&t&&!1===t.preserve&&(s=JSON.parse(JSON.stringify(s))).forEach((s=>{("blank-pseudo-class"===s.id||"prefers-color-scheme-query"===s.id)&&(s.stage=1)}));const l=he(s,a,n).map((s=>function(s){const e=ye(s);if(s[ie]||s[re]){let t=s.id;return t=s.insertBefore?`before-${t}`:`after-${t}`,{browsers:e,vendors_implementations:s.vendors_implementations,plugin:s[ne],id:t,stage:6}}return{browsers:e,vendors_implementations:s.vendors_implementations,plugin:be.get(s.id),id:s.id,stage:s.stage}}(s))).filter((s=>0===p||(!(!s[ie]&&!s[re])||(p<=s.vendors_implementations||(i[s.id]?(o.log(`  ${s.id} does not meet the required vendor implementations but has been enabled by options`),!0):(o.log(`  ${s.id} with ${s.vendors_implementations} vendor implementations has been disabled`),!1)))))).filter((s=>{const e=s.stage>=u,t=r||!Gs.includes(s.id),a=!1===i[s.id],n=i[s.id]?i[s.id]:e&&t;return a?o.log(`  ${s.id} has been disabled by options`):e?t||o.log(`  ${s.id} has been disabled by "enableClientSidePolyfills: false".`):n?o.log(`  ${s.id} does not meet the required stage but has been enabled by options`):o.log(`  ${s.id} with stage ${s.stage} has been disabled`),n})).map((e=>function(s,e,t,o,i,r){let a,n;return a=ke(e,o,s,r),!0===t[o.id]?i&&(a=Object.assign({},a,i)):a=i?Object.assign({},a,i,t[o.id]):Object.assign({},a,t[o.id]),a.enableProgressiveCustomProperties=!1,n=o.plugin.postcss&&"function"==typeof o.plugin?o.plugin(a):o.plugin&&o.plugin.default&&"function"==typeof o.plugin.default&&o.plugin.default.postcss?o.plugin.default(a):o.plugin,{browsers:o.browsers,vendors_implementations:o.vendors_implementations,plugin:n,pluginOptions:a,id:o.id}}(s,c,i,e,t,o))),m=ts.default(c,{ignoreUnknownVersions:!0});return l.filter((s=>{if(s.id in i)return i[s.id];if(function(s){if("importFrom"in Object(s.pluginOptions))switch(s.id){case"custom-media-queries":if(te(s.pluginOptions.importFrom,"customMedia"))return!0;break;case"custom-properties":if(te(s.pluginOptions.importFrom,"customProperties"))return!0;break;case"environment-variables":if(te(s.pluginOptions.importFrom,"environmentVariables"))return!0;break;case"custom-selectors":if(te(s.pluginOptions.importFrom,"customSelectors"))return!0}if("exportTo"in Object(s.pluginOptions))switch(s.id){case"custom-media-queries":if(te(s.pluginOptions.exportTo,"customMedia"))return!0;break;case"custom-properties":if(te(s.pluginOptions.exportTo,"customProperties"))return!0;break;case"environment-variables":if(te(s.pluginOptions.exportTo,"environmentVariables"))return!0;break;case"custom-selectors":if(te(s.pluginOptions.exportTo,"customSelectors"))return!0}return!1}(s))return!0;const e=ts.default(s.browsers,{ignoreUnknownVersions:!0}),t=m.some((s=>e.some((e=>e===s))));return t||o.log(`${s.id} disabled due to browser support`),t}))}class we{constructor(){this.logs=[]}log(s){this.logs.push(s)}resetLogger(){this.logs.length=0}dumpLogs(s){s&&this.logs.forEach((e=>s.warn(e))),this.resetLogger()}}var qe=[{packageName:"css-blank-pseudo",id:"blank-pseudo-class",importName:"postcssBlankPseudo"},{packageName:"css-has-pseudo",id:"has-pseudo-class",importName:"postcssHasPseudo"},{packageName:"css-prefers-color-scheme",id:"prefers-color-scheme-query",importName:"postcssPrefersColorScheme"},{packageName:"postcss-attribute-case-insensitive",id:"case-insensitive-attributes",importName:"postcssAttributeCaseInsensitive"},{packageName:"postcss-clamp",id:"clamp",importName:"postcssClamp"},{packageName:"@csstools/postcss-color-function",id:"color-function",importName:"postcssColorFunction"},{packageName:"postcss-color-functional-notation",id:"color-functional-notation",importName:"postcssColorFunctionalNotation"},{packageName:"postcss-color-hex-alpha",id:"hexadecimal-alpha-notation",importName:"postcssColorHexAlpha"},{packageName:"postcss-color-rebeccapurple",id:"rebeccapurple-color",importName:"postcssColorRebeccapurple"},{packageName:"postcss-custom-media",id:"custom-media-queries",importName:"postcssCustomMedia"},{packageName:"postcss-custom-properties",id:"custom-properties",importName:"postcssCustomProperties"},{packageName:"postcss-custom-selectors",id:"custom-selectors",importName:"postcssCustomSelectors"},{packageName:"postcss-dir-pseudo-class",id:"dir-pseudo-class",importName:"postcssDirPseudoClass"},{packageName:"postcss-double-position-gradients",id:"double-position-gradients",importName:"postcssDoublePositionGradients"},{packageName:"postcss-env-function",id:"environment-variables",importName:"postcssEnvFunction"},{packageName:"postcss-focus-visible",id:"focus-visible-pseudo-class",importName:"postcssFocusVisible"},{packageName:"postcss-focus-within",id:"focus-within-pseudo-class",importName:"postcssFocusWithin"},{packageName:"@csstools/postcss-font-format-keywords",id:"font-format-keywords",importName:"postcssFontFormatKeywords"},{packageName:"postcss-font-variant",id:"font-variant-property",importName:"postcssFontVariant"},{packageName:"postcss-gap-properties",id:"gap-properties",importName:"postcssGapProperties"},{packageName:"@csstools/postcss-hwb-function",id:"hwb-function",importName:"postcssHWBFunction"},{packageName:"@csstools/postcss-ic-unit",id:"ic-unit",importName:"postcssICUnit"},{packageName:"postcss-image-set-function",id:"image-set-function",importName:"postcssImageSetFunction"},{packageName:"postcss-initial",id:"all-property",importName:"postcssInitial"},{packageName:"@csstools/postcss-is-pseudo-class",id:"is-pseudo-class",importName:"postcssIsPseudoClass"},{packageName:"postcss-lab-function",id:"lab-function",importName:"postcssLabFunction"},{packageName:"postcss-logical",id:"logical-properties-and-values",importName:"postcssLogical"},{packageName:"postcss-media-minmax",id:"media-query-ranges",importName:"postcssMediaMinmax"},{packageName:"postcss-nesting",id:"nesting-rules",importName:"postcssNesting"},{packageName:"@csstools/postcss-normalize-display-values",id:"display-two-values",importName:"postcssNormalizeDisplayValues"},{packageName:"@csstools/postcss-oklab-function",id:"oklab-function",importName:"postcssOKLabFunction"},{packageName:"postcss-opacity-percentage",id:"opacity-percentage",importName:"postcssOpacityPercentage"},{packageName:"postcss-overflow-shorthand",id:"overflow-property",importName:"postcssOverflowShorthand"},{packageName:"postcss-page-break",id:"break-properties",importName:"postcssPageBreak"},{packageName:"postcss-place",id:"place-properties",importName:"postcssPlace"},{packageName:"postcss-pseudo-class-any-link",id:"any-link-pseudo-class",importName:"postcssPseudoClassAnyLink"},{packageName:"postcss-replace-overflow-wrap",id:"overflow-wrap-property",importName:"postcssReplaceOverflowWrap"},{packageName:"postcss-selector-not",id:"not-pseudo-class",importName:"postcssSelectorNot"},{packageName:"@csstools/postcss-stepped-value-functions",id:"stepped-value-functions",importName:"postcssSteppedValueFunctions"},{packageName:"postcss-system-ui-font-family",importedPackage:"../patch/postcss-system-ui-font-family.mjs",id:"system-ui-font-family",importName:"postcssFontFamilySystemUI"},{packageName:"@csstools/postcss-unset-value",id:"unset-value",importName:"postcssUnsetValue"},{packageName:"@csstools/postcss-cascade-layers",id:"cascade-layers",importName:"postcssCascadeLayers"},{packageName:"@csstools/postcss-trigonometric-functions",id:"trigonometric-functions",importName:"postcssTrigonometricFunctions"},{packageName:"@csstools/postcss-nested-calc",id:"nested-calc",importName:"postcssNestedCalc"},{packageName:"@csstools/postcss-text-decoration-shorthand",id:"text-decoration-shorthand",importName:"postcssTextDecorationShorthand"}];function $e(s,e,t){const o=qe.map((s=>s.id)),i=qe.map((s=>s.packageName)),r=function(){const s={};return qe.forEach((e=>{s[e.packageName]=e.id})),s}();s.forEach((s=>{if(o.includes(s))return;const a=Oe(s,o),n=Oe(s,i);Math.min(a.distance,n.distance)>10?e.warn(t`Unknown feature: "${s}", see the list of features https://github.com/csstools/postcss-plugins/blob/main/plugin-packs/postcss-preset-env/FEATURES.md`):a.distance<n.distance?e.warn(t,`Unknown feature: "${s}", did you mean: "${a.mostSimilar}"`):e.warn(t,`Unknown feature: "${s}", did you mean: "${r[n.mostSimilar]}"`)}))}function Oe(s,e){let t="unknown",o=1/0;for(let i=0;i<e.length;i++){const r=Se(s,e[i]);r<o&&(o=r,t=e[i])}return{mostSimilar:t,distance:o}}function Se(s,e){if(!s.length)return e.length;if(!e.length)return s.length;const t=[];for(let o=0;o<=e.length;o++){t[o]=[o];for(let i=1;i<=s.length;i++)t[o][i]=0===o?i:Math.min(t[o-1][i]+1,t[o][i-1]+1,t[o-1][i-1]+(s[i-1]===e[o-1]?0:1))}return t[e.length][s.length]}const xe=s=>{const e=new we,t=Object(s),o=Object.keys(Object(t.features)),i=t.browsers,r=function(s){if("importFrom"in s||"exportTo"in s||"preserve"in s){const e={};return"importFrom"in s&&(e.importFrom=s.importFrom),"exportTo"in s&&(e.exportTo={customMedia:{},customProperties:{},customSelectors:{}}),"preserve"in s&&(e.preserve=s.preserve),e}return!1}(t),a=ve(Y.default,t,r,e),n=a.map((s=>s.plugin));!1!==t.autoprefixer&&n.push(X.default(Object.assign({overrideBrowserslist:i},t.autoprefixer))),n.push(Z.default()),function(s,e,t){if(e.debug){t.log("Enabling the following feature(s):");const e=[];s.forEach((s=>{s.id.startsWith("before")||s.id.startsWith("after")?t.log(`  ${s.id} (injected via options)`):t.log(`  ${s.id}`),void 0!==zs[s.id]&&e.push(s.id)})),e.length&&(t.log("These feature(s) need a browser library to work:"),e.forEach((s=>t.log(` ${s}: ${zs[s]}`))))}}(a,t,e);const c=()=>({postcssPlugin:"postcss-preset-env",OnceExit:function(i,{result:a}){$e(o,i,a),t.debug&&e.dumpLogs(a),e.resetLogger(),t.exportTo&&Ys(r.exportTo,s.exportTo)}});return c.postcss=!0,{postcssPlugin:"postcss-preset-env",plugins:[...n,c()]}};xe.postcss=!0,module.exports=xe;
