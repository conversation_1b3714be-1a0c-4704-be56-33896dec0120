# Changes to PostCSS Preset Env

### 7.8.3 (November 14, 2022)

- Update `autoprefixer` to `10.4.13` (patch)
- Update `browserlist` to `4.21.4` (patch)
- Update `postcss-custom-properties` to `12.1.10` (patch)
- Update `@csstools/postcss-cascade-layers` to `1.1.1` (patch)

### 7.8.2 (September 15, 2022)

- Update `@csstools/postcss-cascade-layers` to `1.1.0` (minor)
- Update `autoprefixer` to `10.4.11` (patch)
- Update `postcss-custom-properties` to `12.1.9` (patch)
- Update `postcss-nesting` to `10.2.0` (minor)

### 7.8.1 (September 7, 2022)

- Update `cssdb` to `7.0.1` (patch)
- Update `@csstools/postcss-cascade-layers` to `1.0.6` (patch)

### 7.8.0 (August 16, 2022)

- Added `@csstools/postcss-nested-calc` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-nested-calc#readme) for usage details.
- Added `@csstools/postcss-text-decoration-shorthand` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-text-decoration-shorthand#readme) for usage details.
- Update `@csstools/postcss-cascade-layers` to `1.0.5` (patch)
- Update `@csstools/postcss-color-function` to `1.1.1` (patch)
- Update `@csstools/postcss-font-format-keywords` to `1.0.1` (patch)
- Update `@csstools/postcss-hwb-function` to `1.0.2` (patch)
- Update `@csstools/postcss-ic-unit` to `1.0.1` (patch)
- Update `@csstools/postcss-is-pseudo-class` to `2.0.7` (patch)
- Update `@csstools/postcss-normalize-display-values` to `1.0.1` (patch)
- Update `@csstools/postcss-oklab-function` to `1.1.1` (patch)
- Update `@csstools/postcss-stepped-value-functions` to `1.0.1` (patch)
- Update `@csstools/postcss-trigonometric-functions` to `1.0.2` (patch)
- Update `@csstools/postcss-unset-value` to `1.0.2` (patch)
- Update `autoprefixer` to `10.4.8` (patch)
- Update `browserslist` to `4.21.3` (patch)
- Update `cssdb` to `7.0.0` (major)
- Update `postcss-attribute-case-insensitive` to `5.0.2` (patch)
- Update `postcss-color-functional-notation` to `4.2.4` (patch)
- Update `postcss-color-rebeccapurple` to `7.1.1` (patch)
- Update `postcss-dir-pseudo-class` to `6.0.5` (patch)
- Update `postcss-double-position-gradients` to `3.1.2` (patch)
- Update `postcss-gap-properties` to `3.0.5` (patch)
- Update `postcss-image-set-function` to `4.0.7` (patch)
- Update `postcss-lab-function` to `4.2.1` (patch)
- Update `postcss-nesting` to `10.1.10` (patch)
- Update `postcss-overflow-shorthand` to `3.0.4` (patch)
- Update `postcss-place` to `7.0.5` (patch)
- Update `postcss-pseudo-class-any-link` to `7.1.6` (patch)
- Update `postcss-selector-not` to `6.0.1` (patch)

### 7.7.2 (June 23, 2022)

- Fix `op_mini all` not working as a browser list.
- Updated `postcss-color-rebeccapurple` to `7.1.0` (minor)
- Updated `browserslist` to `4.21.0` (minor)
- Updated `@csstools/postcss-cascade-layers` to `1.0.4` (patch)
- Updated `@csstools/postcss-is-pseudo-class` to `2.0.6` (patch)
- Updated `postcss-color-hex-alpha` to `8.0.4` (patch)
- Updated `postcss-custom-media` to `8.0.2` (patch)
- Updated `postcss-custom-properties` to `12.1.8` (patch)
- Updated `postcss-custom-selectors` to `6.0.3` (patch)
- Updated `postcss-nesting` to `10.1.9` (patch)
- Updated `postcss-pseudo-class-any-link` to `7.1.5` (patch)

### 7.7.1 (June 3, 2022)

- Updated `postcss-selector-not` to `6.0.0` (major)
- Updated `@csstools/postcss-trigonometric-functions` to `1.0.1` (patch)
- Updated `postcss-attribute-case-insensitive` to `5.0.1` (patch)
- Updated `postcss-custom-media` to `8.0.1` (patch)
- Updated `postcss-custom-selectors` to `6.0.2` (patch)
- Updated `cssdb` to `6.6.3` (patch)

### 7.7.0 (May 31, 2022)

- Added `@csstools/postcss-trigonometric-functions` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-trigonometric-functions#readme) for usage details.
- Updated `@csstools/postcss-cascade-layers` to `1.0.2` (patch)
- Updated `postcss-color-functional-notation` to `4.2.3` (patch)
- Updated `postcss-nesting` to `10.1.7` (patch)

### 7.6.0 (May 19, 2022)

- Added `@csstools/postcss-cascade-layers` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-cascade-layers#readme) for usage details.
- Updated `@csstools/postcss-hwb-function` to `1.0.1` (patch)
- Updated `@csstools/postcss-is-pseudo-class` to `2.0.4` (patch)
- Updated `@csstools/postcss-unset-value` to `1.0.1` (patch)
- Updated `postcss-nesting` to `10.1.6` (patch)
- Updated `postcss-pseudo-class-any-link` to `7.1.4` (patch)
- Updated `autoprefixer` to `10.4.7` (patch)

### 7.5.0 (May 2, 2022)

- Added `@csstools/postcss-unset-value` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-unset-value#readme) for usage details.
- Added `@csstools/postcss-stepped-value-functions` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-stepped-value-functions#readme) for usage details.
- Updated `cssdb` to `6.6.0` (minor)
- Updated `autoprefixer` to `10.4.6` (patch)

### 7.4.4 (April 26, 2022)

- Updated `@csstools/postcss-color-function` to `1.1.0` (minor)
- Updated `@csstools/postcss-is-pseudo-class` to `2.0.2` (patch)
- Updated `@csstools/postcss-oklab-function` to `1.1.0` (minor)
- Updated `autoprefixer` to `10.4.5` (patch)
- Updated `browserslist` to `4.20.3` (patch)
- Updated `postcss-custom-properties` to `12.1.7` (patch)
- Updated `postcss-lab-function` to `4.2.0` (minor)
- Updated `postcss-nesting` to `10.1.4` (patch)
- Updated `postcss-pseudo-class-any-link` to `7.1.2` (patch)

### 7.4.3 (March 19, 2022)

- Fix manually enabling a feature that doesn't meet the required vendor implementations. [#310](https://github.com/csstools/postcss-plugins/issues/310)
- Updated `postcss-clamp` to `4.1.0` (minor)
- Updated `@csstools/postcss-color-function` to `1.0.3` (patch)
- Updated `@csstools/postcss-is-pseudo-class` to `2.0.1` (patch)
- Updated `@csstools/postcss-oklab-function` to `1.0.2` (patch)
- Updated `@csstools/postcss-progressive-custom-properties` to `1.3.0` (minor)
- Updated `autoprefixer` to `10.4.4` (patch)
- Updated `browserslist` to `4.20.2` (patch)
- Updated `cssdb` to `6.5.0` (minor)
- Updated `postcss-custom-properties` to `12.1.5` (patch)
- Updated `postcss-double-position-gradients` to `3.1.1` (patch)
- Updated `postcss-env-function` to `4.0.6` (patch)
- Updated `postcss-lab-function` to `4.1.2` (patch)
- Updated `postcss-nesting` to `10.1.3` (patch)
- Remove internal patch for `postcss-clamp`
- Document all features. [https://github.com/csstools/postcss-preset-env/issues/156](https://github.com/csstools/postcss-preset-env/issues/156)

[see the list of features](https://github.com/csstools/postcss-plugins/blob/main/plugin-packs/postcss-preset-env/FEATURES.md)

### 7.4.2 (March 2, 2022)

- Adding internal patch for `postcss-clamp` to fix issue when `clamp` was being used with any other values along. [#274](https://github.com/csstools/postcss-plugins/issues/274)

### 7.4.1 (February 17, 2022) 

- Updated `postcss-clamp` to `4.0.0` (major)

### 7.4.0 (February 16, 2022)

- Added `@csstools/color-function` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-color-function#readme) for usage details.
- Added `@csstools/oklab-function` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-oklab-function#readme) for usage details.
- Added `@csstools/ic-unit` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-ic-unit#readme) for usage details.
- Updated `@csstools/postcss-progressive-custom-properties` to `1.2.0` (minor)
- Updated `cssdb` to `6.3.1` (patch)
- Updated `postcss-double-position-gradients` to `3.1.0` (minor)
- Updated `postcss-lab-function` to `4.1.1` (patch)

### 7.3.3 (February 13, 2022)

- Updated `cssdb` to `6.3.0` (minor)

### 6.7.1 (February 13, 2022)

- Added notice when using preset-env with PostCSS > 7 to prompt to upgrade.

### 7.3.2 (February 12, 2022)

- Updated `postcss-lab-function` to `4.1.0` (minor)

`postcss-lab-function` now supports [wide gamut colors](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-lab-function#displayp3) and [out of gamut color mapping](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-lab-function#displayp3).

- Updated `css-blank-pseudo` to `3.0.3` (patch)
- Updated `css-has-pseudo` to `3.0.4` (patch)
- Updated `cssdb` to `6.2.1` (patch)
- Updated `postcss-color-functional-notation` to `4.2.2` (patch)
- Updated `postcss-color-hex-alpha` to `8.0.3` (patch)
- Updated `postcss-dir-pseudo-class` to `6.0.4` (patch)
- Updated `postcss-double-position-gradients` to `3.0.5` (patch)
- Updated `postcss-env-function` to `4.0.5` (patch)
- Updated `postcss-focus-visible` to `6.0.4` (patch)
- Updated `postcss-focus-within` to `5.0.4` (patch)
- Updated `postcss-gap-properties` to `3.0.3` (patch)
- Updated `postcss-image-set-function` to `4.0.6` (patch)
- Updated `postcss-logical` to `5.0.4` (patch)
- Updated `postcss-overflow-shorthand` to `3.0.3` (patch)
- Updated `postcss-place` to `7.0.4` (patch)
- Updated `postcss-pseudo-class-any-link` to `7.1.1` (patch)
- Updated `@csstools/postcss-progressive-custom-properties` to `1.1.0`.

### 7.3.1 (February 3, 2022) 

- Ensured that `debug` option uses a single instance per run, this allows for `debug` to work on parallel runs within CI environments.
- Normalized exports to support both Common JS and ESM within our modules. This allows for the package to be processable via Webpack [#221](https://github.com/csstools/postcss-plugins/issues/221) 
- Updated `postcss-opacity-percentage` to `1.1.2` (patch).

### 7.3.0 (January 31, 2022)

- Added `@csstools/postcss-is-pseudo-class` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-is-pseudo-class#readme) for usage details.
- Added `@csstools/postcss-hwb-function` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-hwb-function#readme) for usage details.
- Added `postcss-opacity-percentage` <br/> [Check the plugin README](https://github.com/mrcgrtz/postcss-opacity-percentage#readme) for usage details.
- Added `postcss-clamp` <br/> [Check the plugin README](https://github.com/polemius/postcss-clamp#readme) for usage details.
- Added `@csstools/postcss-normalize-display-values` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-normalize-display-values#readme) for usage details.
- Added `@csstools/postcss-font-format-keywords` <br/> [Check the plugin README](https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-font-format-keywords#readme) for usage details.
- Added `debug` [option](https://github.com/csstools/postcss-plugins/tree/main/plugin-packs/postcss-preset-env#debug) that enables extra debugging information while processing the CSS.
- Added `enableClientSidePolyfills` [option](https://github.com/csstools/postcss-plugins/tree/main/plugin-packs/postcss-preset-env#enableclientsidepolyfills) that allows you to control every single plugin that requires a browser library to fully work. Defaults to `true` so they're enabled by default.
- Added `minimumVendorImplementations` [option](https://github.com/csstools/postcss-plugins/tree/main/plugin-packs/postcss-preset-env#minimumvendorimplementations) that allows you to enable/disable plugins based on their implementation status in browsers.
- Fix sourcemaps for `image-set()` function.
- Removed `caniuse-lite` dependency. This results not only in lower package size but also in better feature detection lead by changes on CSSDB.
- Updated `cssdb` to `6.1.0` (major).
- Updated `css-prefers-color-scheme` to `6.0.3` (patch)
- Updated `postcss-custom-properties` to `12.1.4` (patch)
- Updated `postcss-image-set-function` to `4.0.5` (patch)
- Updated `postcss-pseudo-class-any-link` to `7.1.0` (minor)

### 7.2.3 (January 12, 2022)

- Enhanced `importFrom` / `exportTo` so it's harder to cause unexpected issues by different shapes of data.

### 7.2.2 (January 12, 2022)

- Updated `postcss-logical` to `5.0.3` (patch)
- Updated `postcss-custom-properties` to `12.1.2` (patch)

### 7.2.1 (January 12, 2022)

- Always run plugins with side effects if certain options are set. [#140](https://github.com/csstools/postcss-plugins/issues/140)
  - `custom-media-queries`
  - `custom-properties`
  - `environment-variables`
  - `custom-selectors`
- Updated `caniuse-lite` to `1.0.30001299` (minor)
- Updated `css-blank-pseudo` to `3.0.2` (minor)
- Updated `css-has-pseudo` to `3.0.3` (minor)
- Updated `postcss-color-rebeccapurple` to `7.0.2` (minor)
- Updated `postcss-custom-properties` to `12.1.0` (minor)
- Updated `postcss-dir-pseudo-class` to `6.0.3` (minor)
- Updated `postcss-nesting` to `10.1.2` (minor)

This will ensure that CSS transforms that are not a browser polyfill are still applied.
⚠️ A future major version `postcss-preset-env` will remove this behavior completely.

### 7.2.0 (January 2, 2022)

- Added warnings and useful messages when a feature that doesn't exist is configured. [156](https://github.com/csstools/postcss-preset-env/issues/156).

When configured with these options for example: 

```js
options: {
	features: {
		"custom-media": true,
		"postcss-logical": true,
		"postcss-logica": true,
	}
}
```

It will yield the following warnings:

```bash
Unknown feature: "custom-media" did you mean: "custom-media-queries"
Unknown feature: "postcss-logical" did you mean: "logical-properties-and-values"
Unknown feature: "postcss-logica" did you mean: "logical-properties-and-values"
```

- Removed Sourcemaps from package tarball.
- Moved CLI to CLI Package. See [announcement](https://github.com/csstools/postcss-plugins/discussions/121).
- Updated `postcss` to 8.4 (minor)
- Updated `autoprefixer` to 10.4.1 (patch)
- Updated `caniuse-lite` to 1.0.30001295 (patch)
- Updated `css-blank-pseudo` to 3.0.1 (patch)
- Updated `css-has-pseudo` to 3.0.2 (patch)
- Updated `css-prefers-color-scheme` to 6.0.2 (patch)
- Updated `postcss-color-functional-notation` to 4.2.1 (minor)
- Updated `postcss-color-hex-alpha` to 8.0.2 (patch)
- Updated `postcss-custom-properties` to 12.0.2 (patch)
- Updated `postcss-dir-pseudo-class` to 6.0.2 (patch)
- Updated `postcss-double-position-gradients` to 3.0.4 (patch)
- Updated `postcss-env-function` to 4.0.4 (patch)
- Updated `postcss-focus-visible` to 6.0.3 (patch)
- Updated `postcss-focus-within` to 5.0.3 (patch)
- Updated `postcss-gap-properties` to 3.0.2 (patch)
- Updated `postcss-image-set-function` to 4.0.4 (patch)
- Updated `postcss-lab-function` to 4.0.3 (patch)
- Updated `postcss-logical` to 5.0.2 (patch)
- Updated `postcss-nesting` to 10.1.1 (minor)
- Updated `postcss-overflow-shorthand` to 3.0.2 (patch)
- Updated `postcss-place` to 7.0.3 (patch)
- Updated `postcss-pseudo-class-any-link` to 7.0.2 (patch)

### 7.1.0 (December 22, 2021)

- Updated [`postcss-nesting` to `10.1.0` (minor)](https://github.com/csstools/postcss-plugins/blob/main/plugins/postcss-nesting/CHANGELOG.md#changes-to-postcss-nesting)

### 7.0.2 (December 16, 2021)

- Fixed unexpected behaviours when using different versions of `postcss-values-parser` across the plugins [228](https://github.com/csstools/postcss-preset-env/issues/228)
- Updated `browserlist` to 4.19.1 (minor)
- Updated `caniuse-lite` to `1.0.30001287` (minor)
- Updated `css-blank-pseudo` to `3.0.0` (major)
- Updated `css-has-pseudo` to `3.0.0` (major)
- Updated `css-prefers-color-scheme` to `6.0.0` (major)
- Updated `postcss-color-functional-notation` to `4.1.0` (minor)
- Updated `postcss-color-hex-alpha` to `8.0.1` (patch)
- Updated `postcss-color-rebeccapurple` to `7.0.1` (patch)
- Updated `postcss-custom-properties` to `12.0.1` (patch)
- Updated `postcss-dir-pseudo-class` to `6.0.1` (patch)
- Updated `postcss-double-position-gradients` to `3.0.3` (patch)
- Updated `postcss-env-function` to `4.0.3` (patch)
- Updated `postcss-focus-visible` to `6.0.2` (patch)
- Updated `postcss-focus-within` to `5.0.2` (patch)
- Updated `postcss-gap-properties` to `3.0.1` (patch)
- Updated `postcss-image-set-function` to `4.0.3` (patch)
- Updated `postcss-lab-function` to `4.0.2` (patch)
- Updated `postcss-logical` to `5.0.1` (patch)
- Updated `postcss-nesting` to `10.0.3` (patch)
- Updated `postcss-overflow-shorthand` to `3.0.1` (patch)
- Updated `postcss-place` to `7.0.2` (patch)
- Updated `postcss-pseudo-class-any-link` to `7.0.1` (patch)
- Updated documentation
- Updated `postcss` to be a peer dependency.

### 7.0.1 (November 19, 2021)

- Fixed infinite loop in double-position-gradients [223](https://github.com/csstools/postcss-preset-env/issues/223)
- Fixed "Unknown word" errors in when parsing CSS values [224](https://github.com/csstools/postcss-preset-env/issues/224)
- Fixed "undefined" CSS values after transforms with postcss-place [225](https://github.com/csstools/postcss-preset-env/issues/225)
- Updated `postcss-color-functional-notation` to 4.0.1 (patch)
- Updated `postcss-double-position-gradients` to 3.0.1 (patch)
- Updated `postcss-env-function` to 4.0.2 (patch)
- Updated `postcss-image-set-function` to 4.0.2 (patch)
- Updated `postcss-lab-function` to 4.0.1 (patch)
- Updated `postcss-nesting` to 10.0.2 (patch)
- Updated `postcss-place` to 7.0.1 (patch)

### 7.0.0 (November 16, 2021)

- Updated `autoprefixer` to 10.4.0 (major)
- Updated `browserslist` to 4.17.5 (minor)
- Updated `caniuse-lite` to 1.0.30001272 (patch)
- Updated `css-blank-pseudo` to 2.0.0 (major)
- Updated `css-has-pseudo` to 2.0.0 (major)
- Updated `css-prefers-color-scheme` to 5.0.0 (major)
- Updated `cssdb` to 5.0.0 (major)
- Updated `postcss` to 8.3.0 (major)
- Updated `postcss-attribute-case-insensitive` to 5.0.0 (major)
- Updated `postcss-color-functional-notation` to 4.0.0 (major)
- Updated `postcss-color-hex-alpha` to 8.0.0 (major)
- Updated `postcss-color-rebeccapurple` to 7.0.0 (major)
- Updated `postcss-custom-media` to 8.0.0 (major)
- Updated `postcss-custom-properties` to 12.0.0 (major)
- Updated `postcss-custom-selectors` to 6.0.0 (major)
- Updated `postcss-dir-pseudo-class` to 6.0.0 (major)
- Updated `postcss-double-position-gradients` to 3.0.0 (major)
- Updated `postcss-env-function` to 4.0.1 (major)
- Updated `postcss-focus-visible` to 6.0.1 (major)
- Updated `postcss-focus-within` to 5.0.1 (major)
- Updated `postcss-font-variant` to 5.0.0 (major)
- Updated `postcss-gap-properties` to 3.0.0 (major)
- Updated `postcss-image-set-function` to 4.0.0 (major)
- Updated `postcss-initial` to 3.0.4 (patch)
- Updated `postcss-lab-function` to 4.0.0 (major)
- Updated `postcss-logical` to 5.0.0 (major)
- Updated `postcss-media-minmax` to 5.0.0 (major)
- Updated `postcss-nesting` to 10.0.0 (major)
- Updated `postcss-overflow-shorthand` to 3.0.0 (major)
- Updated `postcss-page-break` to 3.0.4 (major)
- Updated `postcss-place` to 7.0.0 (major)
- Updated `postcss-pseudo-class-any-link` to 7.0.0 (major)
- Updated `postcss-replace-overflow-wrap` to 4.0.0 (major)
- Removed `postcss-selector-matches`
- Removed `postcss-color-gray`
- Updated support for Node 12+ (major)

### 6.7.0 (July 8, 2019)

- Fixed the issue of autoprefixer alerting an upcoming change to the API
- Updated `autoprefixer` to 9.6.1 (minor)
- Updated `browserslist` to 4.6.4 (minor)
- Updated `cssdb` to 4.4.0 (minor)
- Updated `caniuse-lite` to 1.0.30000981 (patch)
- Updated `postcss` to 7.0.17 (patch)
- Updated `postcss-color-hex-alpha` to 5.0.3 (patch)
- Updated `postcss-custom-media` to 7.0.8 (patch)
- Updated `postcss-custom-properties` to 8.0.11 (patch)

### 6.6.0 (February 28, 2019)

- Moved browserslist detection from using each input file per process to using
  the working directory on intialization, as was implied by the documentation.
  If fixing this previously undocumented behavior causes any harm to existing
  projects, it can be easily rolled back in a subsequent patch. For the
  majority of projects — those with a singular browserslist configuration and
  potentially many individually processed CSS files — we should expect reported
  build times around 35 seconds to drop to less than 2 seconds.
- Updated `browserslist` to 4.4.2 (minor)
- Updated `autoprefixer` to 9.4.9 (patch)
- Updated `caniuse-lite` to 1.0.30000939 (patch)
- Updated `postcss` to 7.0.14 (patch)
- Updated `postcss-attribute-case-insensitive` to 4.0.1 (patch)

### 6.5.0 (December 12, 2018)

- Added `css-blank-pseudo` polyfill
- Added `css-has-pseudo` polyfill
- Updated `autoprefixer` to 9.4.2 (minor)
- Updated `browserslist` to 4.3.5 (minor)
- Updated `caniuse-lite` to 1.0.30000918 (patch)
- Updated `css-prefers-color-scheme` to 3.1.1 (minor, patch for this project)
- Updated `cssdb` to 4.3.0 (minor)
- Updated `postcss` to 7.0.6 (patch)

### 6.4.0 (November 6, 2018)

- Fixed `exportTo` option to export Custom Media, Custom Properties, and Custom
  Selectors all to the same function, object, or file
- Added `css-prefers-color-scheme` 3.0.0 (major, non-breaking for this project)
- Updated `cssdb` to 4.2.0 (minor)

### 6.3.1 (November 5, 2018)

- Updated `caniuse-lite` to 1.0.30000905 (patch)
- Updated `postcss-custom-properties` to 8.0.9 (patch)

### 6.3.0 (October 28, 2018)

- Added `postcss-double-position-gradients` 1.0.0 (major, non-breaking for this project)
- Updated `autoprefixer` to 9.3.1 (minor)
- Updated `browserslist` to 4.3.4 (patch)
- Updated `caniuse-lite` to 1.0.30000899 (patch)
- Updated `cssdb` to 4.1.0 (major, non-breaking for this project)

### 6.2.0 (October 22, 2018)

- Updated `autoprefixer` to 9.2.1 (minor)
- Updated `browserslist` to 4.3.1 (minor)

### 6.1.2 (October 19, 2018)

- Updated `browserslist` to 4.2.1 (patch)
- Updated `caniuse-lite` to 1.0.30000893 (patch)
- Updated `postcss-custom-media` to 7.0.7 (patch)

### 6.1.1 (October 12, 2018)

- Updated: `postcss-custom-media` to 7.0.6 (patch)

### 6.1.0 (October 10, 2018)

- Added: `postcss-color-gray`
- Added: Passing `autoprefixer: false` disables autoprefixer
- Updated: `browserslist` to 4.2.0 (minor)
- Updated: `caniuse-lite` to 1.0.30000890 (patch)

### 6.0.10 (October 2, 2018)

- Updated: `postcss-custom-properties` to 8.0.8 (patch)

### 6.0.9 (October 2, 2018)

- Updated: `browserslist` to 4.1.2 (patch)
- Updated: `postcss` to 7.0.5 (patch)
- Updated: `postcss-custom-properties` to 8.0.7 (patch)

### 6.0.8 (October 1, 2018)

- Updated: `caniuse-lite` to 1.0.30000888 (patch)
- Updated: `postcss` to 7.0.4 (patch)

**Did you hear? PostCSS Preset Env is now part of Create React App!** 🎉

### 6.0.7 (September 23, 2018)

- Updated: `postcss` to 7.0.3 (patch)
- Updated: `postcss-custom-properties` to 8.0.6 (patch)

### 6.0.6 (September 23, 2018)

- Updated: `postcss-custom-media` to 7.0.4 (patch)

### 6.0.5 (September 23, 2018)

- Updated: `postcss-color-mod-function` to 3.0.3 (patch)

### 6.0.4 (September 23, 2018)

- Updated: `caniuse-lite` to 1.0.30000887 (patch)
- Updated: `postcss-color-mod-function` to 3.0.2 (patch)

### 6.0.3 (September 21, 2018)

- Updated: `caniuse-lite` to 1.0.30000885 (patch)
- Updated: `postcss-custom-properties` to 8.0.5 (patch)

### 6.0.2 (September 20, 2018)

- Fixed: Do not break on an empty `importFrom` object
- Fixed: Actually run `postcss-env-function`

### 6.0.1 (September 20, 2018)

- Fixed: Issue with the `system-ui` font family polyfill by replacing
  `postcss-font-family-system-ui` with an internal polyfill, at least until the
  problem with the original plugin is resolved.

### 6.0.0 (September 20, 2018)

- Added: Support for PostCSS 7+
- Added: Support for PostCSS Values Parser 2+
- Added: Support for PostCSS Selector Parser 5+
- Added: Support for Node 6+
- Updated: All 28 plugins

### 5.4.0 (July 25, 2018)

- Added: `toggle` option to override which features are enabled or disabled
- Deprecated: toggle features with `toggle`, not `features`

### 5.3.0 (July 24, 2018)

- Updated: `postcss-lab-function` to v1.1.0 (minor update)

### 5.2.3 (July 21, 2018)

- Updated: `postcss-color-mod-function` to v2.4.3 (patch update)

### 5.2.2 (July 13, 2018)

- Updated: `autoprefixer` to v8.6.5 (patch update)
- Updated: `caniuse-lite` to v1.0.30000865 (patch update)
- Updated: `postcss-color-functional-notation` to v1.0.2 (patch update)

### 5.2.1 (June 26, 2018)

- Updated: `caniuse-lite` to v1.0.30000859 (patch update)
- Updated: `postcss-attribute-case-insensitive` to v3.0.1 (patch update)

### 5.2.0 (June 25, 2018)

- Updated: `autoprefixer` to v8.6.3 (minor update)
- Updated: `caniuse-lite` to v1.0.30000858 (patch update)
- Updated: `postcss` to 6.0.23 (patch update)
- Updated: `postcss-nesting` to v6.0.0 (major internal update, non-breaking for this project)

### 5.1.0 (May 21, 2018)

- Added: `autoprefixer` option to pass options into autoprefixer
- Updated: `autoprefixer` to v8.5.0 (minor update)
- Updated: `browserslist` to v3.2.8 (patch update)
- Updated: `caniuse-lite` to v1.0.30000844 (patch update)
- Updated: `postcss-color-functional-notation` to v1.0.1 (patch update)

### 5.0.0 (May 11, 2018)

- Added: `autoprefixer`
- Added: `postcss-color-functional-notation`
- Added: `postcss-env-function`
- Added: `postcss-lab-function`
- Added: `postcss-place`
- Added: `postcss-gap-properties`
- Added: `postcss-overflow-shorthand`
- Updated: `cssdb` to v3.1.0 (major update)
- Updated: In conformance with cssdb v3, the default stage is now 2
- Updated: `postcss-attribute-case-insensitive` to v3.0.0 (major update)
- Updated: `postcss-pseudo-class-any-link` to v5.0.0 (major update)
- Updated: `postcss-image-set-function` to v2.0.0 (major update)
- Updated: `postcss-dir-pseudo-class` to v4.0.0 (major update)
- Updated: `postcss-color-rebeccapurple` to v3.1.0 (minor update)
- Updated: `postcss` to v6.0.22 (patch update)
- Updated: `browserslist` to v3.2.7 (patch update)
- Updated: `caniuse-lite` to v1.0.30000839 (patch update)

All plugins now conform to the latest stable releases of `postcss-value-parser`
v1.5.0 and `postcss-selector-parser` v4.0.0.

### 4.1.0 (April 23, 2018)

- Updated: `browserslist` to v3.2.5 (patch update)
- Updated: `caniuse-lite` to v1.0.30000830 (patch update)
- Updated: `postcss-apply` to v0.10.0 (minor update)
- Updated: `postcss-nesting` to v5.0.0 (major update, non-breaking for this project)

### 4.0.0 (April 7, 2018)

- Added: `postcss-focus-within`
- Updated: `postcss-focus-visible` to v3.0.0 (major update)
- Updated: `caniuse-lite` to v1.0.30000824 (patch update)
- Updated: `cssdb` to v2.0.0 (major update)
- Changed: All `specificationId` names to new `id` names for the `cssdb` update.

### 3.5.0 (April 5, 2018)

- Fixed: `selectors-matches-pseudo` mapping to allow `:matches` polyfilling
- Updated: `postcss-dir-pseudo-class` to v3.0.0 (major update, non-breaking for this project)
- Updated: `postcss-logical` to v1.1.1 (minor update)
- Updated: `postcss` to v6.0.21 (patch update)
- Updated: `browserslist` to v3.2.4 (patch update)
- Updated: `caniuse-lite` to v1.0.30000823 (patch update)

### 3.4.0 (March 18, 2018)

- Updated: `browserslist` to v3.2.0 (minor update)
- Updated: `postcss` to v6.0.20 (patch update)
- Updated: `postcss-image-set-polyfill` to `@csstools/postcss-image-set-function` (hopefully temporarily)

### 3.3.0 (March 16, 2018)

- Updated: `postcss-apply` to v0.9.0 (minor update)
- Updated: `browserslist` to v3.1.2 (patch update)
- Updated: `caniuse-lite` to v1.0.30000815 (patch update)
- Updated: distribution to cjs and es bundles

### 3.2.2 (February 27, 2018)

- Updated: `postcss-color-mod-function` to v2.4.2 (patch update)

### 3.2.1 (February 21, 2018)

- Updated: Use the latest tested version of all dependencies

### 3.2.0 (February 18, 2018)

- Added: `postcss-page-break` which has moved here from Autoprefixer

### 3.1.0 (February 17, 2018)

- Added: `postcss-focus-visible`

### 3.0.0 (February 16, 2018)

- Updated: `postcss-color-mod-function` to v2.4 (minor update)
- Updated: `postcss-custom-properties` to v7.0 (major update)

### 2.2.0 (February 14, 2018)

- Updated: `browserslist` to v3.1 (major update)
- Updated: `postcss-color-mod-function` to v2.3 (minor update)
- Improved: cleaned up one reusable variable and added a few tests

### 2.1.0 (January 22, 2018)

- Updated: `cssdb` to v1.5 (minor update)
- Updated: `postcss-color-mod-function` to v2.2 (major update)
- Updated: `postcss-font-family-system-ui` to v3.0 (repo update)

### 2.0.0 (January 16, 2018)

- Initial version

### 1.0.0 (December 20, 2017)

- Unsupported version accidentally published by a member of the community
